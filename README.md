# pearl-backend

The [Pearl](https://heypearl.ai) backend monorepo.

## Table of contents

- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Migrations](#migrations)
- [Run](#run)
- [Tests](#tests)
- [Tools & IDE](#tools--ide)
- [Directory Overview](#directory-overview)

## Prerequisites

> These prerequisites apply system-wide. They must be installed only once.

### Xcode

Install the latest stable [Xcode](https://developer.apple.com/xcode/resources/).
Once installed, open the app and accept the licence agreement.

### Homebrew

Install [Homebrew](https://brew.sh/), a package manager for macOS.

```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

### Clone repository

```
git clone https://github.com/heypearl-ai/pearl-backend && cd pearl-backend
```

> ⚠️ All commands that follow must be run with the root folder `pearl-backend` as your current directory.

For cloning via HTTPS
a [personal access token](https://help.github.com/en/github/authenticating-to-github/creating-a-personal-access-token-for-the-command-line)
is needed when 2 factor authentication is enabled.
Alternatively, you can clone via SSH after setting up
an [SSH key](https://help.github.com/en/github/using-git/which-remote-url-should-i-use#cloning-with-ssh-urls).

## Installation

### Install dependencies

All the dependencies listed in the [Brewfile](./Brewfile) can be installed at once by running:

```bash
brew bundle
```

### PyEnv

PyEnv is installed by Homebrew, but you may wish to configure **automatic loading**. In that case, run:

```bash
pyenv init
```

... and follow the instructions.

### Python

Install the right Python version for this project:

```bash
pyenv install 3.12.8
```

Make sure to start a new shell session or to source your profile file to apply the Python version changes.

### Poetry

Install poetry >=2 with the [poetry installation guide](https://python-poetry.org/docs/#installation).

> ⚠️ The [poetry Homebrew formula](https://formulae.brew.sh/formula/poetry) is not officially supported and recommended,
> so we don't use it.

### Python packages

Install dependencies from poetry project:

```bash
poetry install
```

> ☝️ The rest of the instructions assumes that you are inside the poetry virtual
> environment, which should be the case after direnv has been setup.
>
> Instead of using direnv you can also either:
>
> - activate the pipenv virtual environment using `poetry shell`
> - prepend `poetry run` before commands

### Git configuration

We use https://pre-commit.com to manage pre-commit & pre-push hooks

Setup the pre-commit hooks:

```bash
pre-commit install --hook-type pre-push    # for pre-push (mandatory)
pre-commit install --hook-type pre-commit  # for pre-commit (optional)
```

If you want to test hooks

```bash
pre-commit run --all-files
```

### Setup PostgreSQL database

```bash
# Create the dev and test dbs
createdb pearl_backend
createdb pearl_backend_test
```

It is recommended to set the default timezone of your newly installed PostgreSQL server to **UTC**:

Check the current timezone:

```bash
psql pearl_backend -c "SHOW TIMEZONE;"
```

Edit this file to have "timezone = UTC"

```bash
sudo vim /opt/homebrew/var/postgresql@16/postgresql.conf
```

Note: `psql pearl_backend -c "SHOW config_file;"` will reveal the pearl_backend path, if it's not this one

Then reload the server

```bash
psql pearl_backend -c "SELECT pg_reload_conf();"
psql pearl_backend -c "SHOW TIMEZONE;"

# run migrations
alembic upgrade head
```

## Migrations

https://alembic.sqlalchemy.org/en/latest/tutorial.html

```bash
alembic revision --autogenerate -m "<your change>"
alembic upgrade head
alembic current
alembic history

# check and merge multiple heads
./check_merge.sh
./check_merge.sh --help
```

## Run

```bash
poetry run fastapi dev app/main.py
```

## Tests

```bash
poetry run pytest
```

## Tools & IDE

### Ruff: linter and code formatter

There are several [ways](https://docs.astral.sh/ruff/editors/setup/#pycharm) to setup Pycharm

**File Watcher**

![img.png](docs/readme/ruff-check-file-watcher.png)
_Note_: If you do this, ensure that “Auto-save edited files to trigger the watcher” is disabled to avoid the code being
auto-formatted as you are typing.

![img.png](docs/readme/ruff-format-file-watcher.png)
_Note_: If you do this, ensure that “Auto-save edited files to trigger the watcher” is disabled to avoid the code being
auto-formatted as you are typing.

![img.png](docs/readme/actions-on-save.png)

**External tool**

https://docs.astral.sh/ruff/editors/setup/#via-external-tool

**Plugin**

https://plugins.jetbrains.com/plugin/20574-ruff

### mypy: type checking

https://mypy.readthedocs.io/  
config: pyproject.toml  
pre-commit & pre-push: enabled

```bash
mypy .
pre-commit run mypy --all-files
```

we should not need any configuration for PyCharm which should be already checking type hinting & pep484

## Directory Overview

### Dependency Graph

- **`core`:** Base. All modules use it.
- **`common`:** Contains common utilities. Independent.
- **`admin`:** Admin app. Uses core.
- **`auth`:** Uses core. No dependency on others.
- **`integrations`:** Connects to external services and creates intelligence from the data.
- **`workspace`:** Handles business logic and config. Can import integrations.
- **`agentic`:** Uses integrations for agent context & tools. Gets config from workspace.
- **`cli`:** Command-line tools. May use other modules as needed.

The idea is to **keep dependencies flowing in one direction** so no two modules end up depending on each other. This makes the architecture maintainable and easier to extend.

### Where Are the Models?

Each module (e.g., auth, workspace, agentic) contains its own models (ORM classes and Pydantic schemas), which improves clarity and keeps the codebase more modular.

> **Note:** Migrations for the database (Alembic) typically live in a separate top-level directory (e.g., `alembic/` or `migrations/`) alongside `app/`.
