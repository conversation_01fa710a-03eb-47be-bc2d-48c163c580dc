from app.integrations.processors.embedders.noop_embedder import <PERSON><PERSON><PERSON><PERSON>dder


def test_embed_text_returns_vector_of_expected_shape():
    embedder = NoopEmbedder(dim=768)

    text = "This is a test document for embedding."
    embedding = embedder.embed_text(text)

    assert isinstance(embedding, list)
    assert len(embedding) == 768
    assert all(isinstance(x, float) for x in embedding)
