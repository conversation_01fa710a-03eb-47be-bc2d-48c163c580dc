from app.integrations.processors.embedders.noop_embedder import <PERSON><PERSON><PERSON>mbedder


def test_noop_embedder_returns_correct_shape_default():
    embedder = NoopEmbedder()
    vector = embedder.embed_text("whatever")
    assert isinstance(vector, list)
    assert len(vector) == 768
    assert all(isinstance(x, float) for x in vector)


def test_noop_embedder_custom_dimension():
    embedder = NoopEmbedder(dim=1536)
    vector = embedder.embed_text("test")
    assert len(vector) == 1536
