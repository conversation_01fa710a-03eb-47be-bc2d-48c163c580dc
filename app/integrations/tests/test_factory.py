import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import Async<PERSON><PERSON><PERSON><PERSON>oc<PERSON>
from app.integrations.base.crm_backend import BaseCRMBackend
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.base.messaging_backend import BaseMessagingBackend
from app.integrations.context import IntegrationContext, IntegrationContextFactory
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.handles import CRMHandle, FileHandle, MessagingHandle
from app.integrations.types import IntegrationSource


def implements_crm_resource(obj):
    required_attrs = [
        "source",
        "get_opportunity",
        "update_opportunity",
        "list_opportunities_by_account",
        "get_account",
        "list_account_access",
    ]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_messaging_resource(obj):
    required_attrs = [
        "source",
        "search_channel_messages",
        "start_channel_ingestion",
        "start_channel_processing",
    ]
    return all(hasattr(obj, attr) for attr in required_attrs)


def implements_file_resource(obj):
    required_attrs = [
        "source",
        "upload_file",
    ]
    return all(hasattr(obj, attr) for attr in required_attrs)


@pytest.fixture
def db_session_factory():
    return lambda: None


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_context(mocker, tenant_id, db_session_factory):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = tenant_id
    mock_context.db_session_factory = db_session_factory
    mock_context.credentials_resolver = mocker.Mock()
    return mock_context


@pytest.fixture
def mock_integration_factory(mock_context):
    return IntegrationFactory(context=mock_context)


def test_crm(mocker, mock_integration_factory):
    mock_crm_backend = mocker.Mock(spec=BaseCRMBackend)
    mock_crm_backend.source = IntegrationSource.SALESFORCE
    mocker.patch.object(
        mock_integration_factory, "_create_backend", return_value=mock_crm_backend
    )

    result = mock_integration_factory.crm(source=IntegrationSource.SALESFORCE)

    mock_integration_factory._create_backend.assert_called_once()
    assert isinstance(result, CRMHandle)
    assert result.source == IntegrationSource.SALESFORCE
    assert implements_crm_resource(result)


def test_messaging(mocker, mock_integration_factory):
    mock_messaging_backend = mocker.Mock(spec=BaseMessagingBackend)
    mock_messaging_backend.source = IntegrationSource.SLACK

    mocker.patch.object(
        mock_integration_factory, "_create_backend", return_value=mock_messaging_backend
    )

    result = mock_integration_factory.messaging(source=IntegrationSource.SLACK)

    mock_integration_factory._create_backend.assert_called_once()
    assert isinstance(result, MessagingHandle)
    assert result.source == IntegrationSource.SLACK
    assert implements_messaging_resource(result)


def test_file(mocker, mock_integration_factory):
    mock_file_backend = mocker.Mock(spec=BaseFileBackend)
    mock_file_backend.source = IntegrationSource.GCS

    mocker.patch.object(
        mock_integration_factory, "_create_backend", return_value=mock_file_backend
    )

    result = mock_integration_factory.file(source=IntegrationSource.GCS)

    mock_integration_factory._create_backend.assert_called_once()
    assert isinstance(result, FileHandle)
    assert result.source == IntegrationSource.GCS
    assert implements_file_resource(result)


def test_crm_wrong_type(mocker, mock_integration_factory):
    mock_non_crm_backend = mocker.Mock()  # Not a CRM backend
    mocker.patch.object(
        mock_integration_factory, "_create_backend", return_value=mock_non_crm_backend
    )

    with pytest.raises(
        TypeError, match="Created backend does not implement BaseCRMBackend"
    ):
        mock_integration_factory.crm(source=IntegrationSource.SALESFORCE)


def test_messaging_wrong_type(mocker, mock_integration_factory):
    mock_non_messaging_backend = mocker.Mock()  # Not a messaging backend
    mocker.patch.object(
        mock_integration_factory,
        "_create_backend",
        return_value=mock_non_messaging_backend,
    )

    with pytest.raises(
        TypeError, match="Created backend does not implement BaseMessagingBackend"
    ):
        mock_integration_factory.messaging(source=IntegrationSource.SLACK)


def test_file_wrong_type(mocker, mock_integration_factory):
    mock_non_file_backend = mocker.Mock()  # Not a file backend
    mocker.patch.object(
        mock_integration_factory, "_create_backend", return_value=mock_non_file_backend
    )

    with pytest.raises(
        TypeError, match="Created backend does not implement BaseFileBackend"
    ):
        mock_integration_factory.file(source=IntegrationSource.GCS)


def test_create_backend(mocker, mock_integration_factory):
    from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
    from app.integrations.backends.crm.backend import CRMBackend
    from app.integrations.types import BackendType

    mock_mapping = {
        IntegrationSource.SALESFORCE: {
            BackendType.CRM: (SalesforceAdapter, CRMBackend),
        },
    }

    mocker.patch.object(
        mock_integration_factory, "_get_integration_mapping", return_value=mock_mapping
    )

    mock_backend = mocker.Mock()
    backend_class_mock = mocker.patch.object(
        CRMBackend, "__new__", return_value=mock_backend
    )

    result = mock_integration_factory._create_backend(
        source=IntegrationSource.SALESFORCE,
        type_=BackendType.CRM,
    )

    assert result == mock_backend
    backend_class_mock.assert_called_once()


def test_create_backend_unsupported_source(mocker, mock_integration_factory):
    from app.integrations.types import BackendType

    mocker.patch.object(
        mock_integration_factory, "_get_integration_mapping", return_value={}
    )

    with pytest.raises(ValueError, match="Unsupported source"):
        mock_integration_factory._create_backend(
            source=IntegrationSource.SLACK,
            type_=BackendType.CRM,
        )

    with pytest.raises(ValueError, match="Unsupported source"):
        mock_integration_factory._create_backend(
            source=IntegrationSource.GCS,
            type_=BackendType.CRM,
        )


def test_create_backend_unsupported_type(mocker, mock_integration_factory):
    from app.integrations.types import BackendType

    mock_mapping = {
        IntegrationSource.SALESFORCE: {
            BackendType.CRM: (mocker.Mock(), mocker.Mock()),
        },
        IntegrationSource.GCS: {
            BackendType.FILE: (mocker.Mock(), mocker.Mock()),
        },
    }

    mocker.patch.object(
        mock_integration_factory, "_get_integration_mapping", return_value=mock_mapping
    )

    with pytest.raises(ValueError, match="Unsupported backend type"):
        mock_integration_factory._create_backend(
            source=IntegrationSource.SALESFORCE,
            type_=BackendType.MESSAGING,
        )

    with pytest.raises(ValueError, match="Unsupported backend type"):
        mock_integration_factory._create_backend(
            source=IntegrationSource.GCS,
            type_=BackendType.CRM,
        )


def test_get_integration_mapping():
    from app.integrations.adapters.salesforce.adapter import SalesforceAdapter
    from app.integrations.adapters.slack.adapter import SlackAdapter
    from app.integrations.backends.crm.backend import CRMBackend
    from app.integrations.backends.messaging.backend import MessagingBackend
    from app.integrations.types import BackendType

    mapping = IntegrationFactory._get_integration_mapping()

    assert IntegrationSource.SALESFORCE in mapping
    assert BackendType.CRM in mapping[IntegrationSource.SALESFORCE]

    crm_adapter_class, crm_backend_class = mapping[IntegrationSource.SALESFORCE][
        BackendType.CRM
    ]
    assert crm_adapter_class == SalesforceAdapter
    assert crm_backend_class == CRMBackend

    assert IntegrationSource.SLACK in mapping
    assert BackendType.MESSAGING in mapping[IntegrationSource.SLACK]

    messaging_adapter_class, messaging_backend_class = mapping[IntegrationSource.SLACK][
        BackendType.MESSAGING
    ]
    assert messaging_adapter_class == SlackAdapter
    assert messaging_backend_class == MessagingBackend


def test_create_factory_with_defaults(mocker, tenant_id, mock_context):
    mocker.patch.object(
        IntegrationContextFactory, "create_context", return_value=mock_context
    )

    factory_instance = mocker.Mock(spec=IntegrationFactory)
    factory_class_mock = mocker.patch(
        "app.integrations.factory.IntegrationFactory", return_value=factory_instance
    )

    result = create_factory(tenant_id)

    IntegrationContextFactory.create_context.assert_called_once_with(
        tenant_id=tenant_id,
        credentials_resolver=None,
        db_session=None,
        db_session_factory=AsyncSessionLocal,
    )

    factory_class_mock.assert_called_once_with(mock_context)
    assert result == factory_instance


def test_create_factory_with_custom_params(mocker, tenant_id, mock_context):
    mocker.patch.object(
        IntegrationContextFactory, "create_context", return_value=mock_context
    )

    factory_instance = mocker.Mock(spec=IntegrationFactory)
    factory_class_mock = mocker.patch(
        "app.integrations.factory.IntegrationFactory", return_value=factory_instance
    )

    def custom_db_factory():
        return mocker.Mock(spec=AsyncSession)

    custom_credentials_resolver = mocker.Mock()

    result = create_factory(
        tenant_id=tenant_id,
        db_session_factory=custom_db_factory,
        credentials_resolver=custom_credentials_resolver,
    )

    IntegrationContextFactory.create_context.assert_called_once_with(
        tenant_id=tenant_id,
        credentials_resolver=custom_credentials_resolver,
        db_session=None,
        db_session_factory=custom_db_factory,
    )

    factory_class_mock.assert_called_once_with(mock_context)
    assert result == factory_instance


def test_create_factory_integration(tenant_id):
    def custom_db_factory():
        return None

    result = create_factory(tenant_id, db_session_factory=custom_db_factory)

    assert isinstance(result, IntegrationFactory)
    assert isinstance(result.context, IntegrationContext)
    assert result.context.tenant_id == tenant_id
    assert result.context.credentials_resolver is None
