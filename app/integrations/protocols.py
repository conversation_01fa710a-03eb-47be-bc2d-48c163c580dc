from collections.abc import Callable
from typing import Any, BinaryIO, Protocol

from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.schemas import DocumentData
from app.integrations.types import IntegrationSource


class BaseIntegrationResource(Protocol):
    """Base protocol for all integration resources."""

    @property
    def source(self) -> IntegrationSource:
        """Gets the source integration."""
        ...


class CRMResource(BaseIntegrationResource, Protocol):
    # Core CRM operations
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        """Gets an opportunity by ID."""
        ...

    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates an opportunity."""
        ...

    async def list_opportunities_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists opportunities for an account."""
        ...

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches opportunities by criteria."""
        ...

    async def get_account(self, account_id: str) -> dict[str, Any]:
        """Gets an account by ID."""
        ...

    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates an account."""
        ...

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches accounts by criteria."""
        ...

    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        """Gets a contact by ID."""
        ...

    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new contact."""
        ...

    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates a contact."""
        ...

    async def list_contacts_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists contacts for an account."""
        ...

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        """Searches contacts by criteria."""
        ...

    async def get_task(self, task_id: str) -> dict[str, Any]:
        """Gets a task by ID."""
        ...

    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new task."""
        ...

    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates a task."""
        ...

    async def list_tasks_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists tasks for a contact."""
        ...

    async def list_tasks_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists tasks for an account."""
        ...

    async def get_event(self, event_id: str) -> dict[str, Any]:
        """Gets an event by ID."""
        ...

    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        """Creates a new event."""
        ...

    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Updates an event."""
        ...

    async def list_events_by_contact(
        self, contact_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists events for a contact."""
        ...

    async def list_events_by_account(
        self, account_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists events for an account."""
        ...

    async def list_account_access(
        self, crm_user_id: str, limit: int = 100, offset: int = 0
    ) -> list[dict[str, Any]]:
        """Lists accounts that the specified user has access to."""
        ...

    # Sync operations
    async def bulk_sync_account_access(
        self,
        crm_user_ids: list[str],
        get_credentials_resolver: Callable[[str], ICredentialsResolver] | None = None,
        interval_seconds: int = 300,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        """Starts synchronization of account access for the specified users."""
        ...


class MessagingResource(BaseIntegrationResource, Protocol):
    async def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        """Searches for messages in the specified channel."""
        ...

    async def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        """Starts ingestion of messages from the specified channels."""
        ...

    async def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        """Starts processing of messages from the specified channels."""
        ...


class FileResource(BaseIntegrationResource, Protocol):
    async def upload_file(
        self, bucket_name: str, file_obj: BinaryIO, file_name: str
    ) -> None:
        """Uploads a document"""
        ...
