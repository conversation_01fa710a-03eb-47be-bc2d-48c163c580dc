from abc import ABC, abstractmethod
from typing import Binary<PERSON>

from app.integrations.base.backend import BaseBackend
from app.integrations.types import BackendType


class BaseFileBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.FILE

    @abstractmethod
    async def upload_file(
        self,
        bucket_name: str,
        file_obj: BinaryIO,
        file_name: str,
    ) -> None:
        pass

    @abstractmethod
    async def start_processing(
        self,
        bucket_name: str,
    ) -> None:
        pass
