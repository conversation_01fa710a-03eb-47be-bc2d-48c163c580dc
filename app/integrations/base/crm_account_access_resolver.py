from abc import ABC, abstractmethod

from app.integrations.schemas import CRMAccountAccessData


class ICRMAccountAccessResolver(ABC):
    """
    Interface defining a resolver for retrieving CRM account access.
    Allows customizing access logic for different clients.
    """

    @abstractmethod
    def get_user_account_access(self, user_id: str) -> list[CRMAccountAccessData]:
        """
        Retrieve all accounts a user has access to according to the specific resolver.
        Returns: List of CRMAccountAccessData objects containing access information
        """
        pass
