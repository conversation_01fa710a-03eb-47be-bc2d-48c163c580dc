from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.adapter import BaseAdapter
from app.integrations.schemas import CRMAccountAccessData


class BaseCRMAdapter(BaseAdapter, ABC):
    @abstractmethod
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_account(self, account_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        pass

    @abstractmethod
    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_task(self, task_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        pass

    @abstractmethod
    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_tasks_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_event(self, event_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        pass

    @abstractmethod
    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_events_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def list_events_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        pass
