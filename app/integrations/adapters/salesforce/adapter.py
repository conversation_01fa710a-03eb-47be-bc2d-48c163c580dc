from typing import Any

from app.integrations.adapters.salesforce.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.schemas import CRMAccountAccessData
from app.integrations.types import IntegrationSource


class SalesforceAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._handler = SalesforceHandler(credentials=credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self._handler.get_opportunity(opportunity_id)

    async def update_opportunity(
        self, opportunity_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._handler.update_opportunity(opportunity_id, fields)

    async def list_opportunities_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_opportunities_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_opportunities(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self._handler.get_account(account_id)

    async def update_account(
        self, account_id: str, fields: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._handler.update_account(account_id, fields)

    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_accounts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        return await self._handler.resolve_account_access(
            salesforce_user_id=crm_user_id
        )

    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self._handler.get_contact(contact_id)

    async def create_contact(self, contact_data: dict[str, Any]) -> dict[str, Any]:
        return await self._handler.create_contact(contact_data)

    async def update_contact(
        self, contact_id: str, contact_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._handler.update_contact(contact_id, contact_data)

    async def list_contacts_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_contacts_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.search_contacts(
            search_criteria=search_criteria, limit=limit, offset=offset
        )

    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self._handler.get_task(task_id)

    async def create_task(self, task_data: dict[str, Any]) -> dict[str, Any]:
        return await self._handler.create_task(task_data)

    async def update_task(
        self, task_id: str, task_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._handler.update_task(task_id, task_data)

    async def list_tasks_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_tasks_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_tasks_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_tasks_by_account(
            account_id=account_id, limit=limit, offset=offset
        )

    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self._handler.get_event(event_id)

    async def create_event(self, event_data: dict[str, Any]) -> dict[str, Any]:
        return await self._handler.create_event(event_data)

    async def update_event(
        self, event_id: str, event_data: dict[str, Any]
    ) -> dict[str, Any]:
        return await self._handler.update_event(event_id, event_data)

    async def list_events_by_contact(
        self,
        contact_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_events_by_contact(
            contact_id=contact_id, limit=limit, offset=offset
        )

    async def list_events_by_account(
        self,
        account_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        return await self._handler.list_events_by_account(
            account_id=account_id, limit=limit, offset=offset
        )
