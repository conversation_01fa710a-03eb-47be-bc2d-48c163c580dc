import time
from collections.abc import Callable
from functools import wraps
from typing import Any, Optional, TypeVar, cast

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from app.common.helpers.logger import get_logger

logger = get_logger()

F = TypeVar("F", bound=Callable[..., Any])


class SlackClientError(Exception):
    pass


class SlackClient:
    def __init__(
        self,
        token: str,
    ):
        self.token = token

    def _create_client(self) -> WebClient:
        try:
            return WebClient(token=self.token)
        except Exception as e:
            logger.exception("Failed to create Slack client")
            raise SlackClientError(f"Client initialization failed: {str(e)}")

    @property
    def _client(self) -> WebClient:
        if not hasattr(self, "_lazy_client"):
            self._lazy_client = self._create_client()
        return self._lazy_client

    @staticmethod
    def _handle_slack_api_errors_with_retry(
        max_retries: int = 5,
        initial_wait: float = 1.0,
        max_wait: float = 60.0,
        factor: float = 2.0,
    ):
        """
        Decorator that implements error handling and exponential backoff retries for Slack API calls.

        Handles both general API errors and rate limit errors by retrying with backoff.

        Args:
            max_retries: Maximum number of retry attempts (total attempts including initial)
            initial_wait: Initial wait time in seconds
            max_wait: Maximum wait time in seconds
            factor: Multiplier for the wait time after each retry
        """

        def decorator(func: F) -> F:
            @wraps(func)
            def wrapper(*args, **kwargs):
                retries = 0
                wait_time = initial_wait
                last_exception = None

                while retries <= max_retries:
                    try:
                        return func(*args, **kwargs)
                    except SlackApiError as e:
                        last_exception = e
                        if e.response.get("error") == "ratelimited":
                            # Get retry_after from headers if available, otherwise use exponential backoff
                            retry_after = float(
                                e.response.headers.get("Retry-After", wait_time)
                            )
                            logger.warning(
                                f"Rate limited by Slack API. Retrying after {retry_after} seconds (attempt {retries + 1}/{max_retries})."
                            )
                            time.sleep(retry_after)
                            retries += 1
                            wait_time = min(wait_time * factor, max_wait)
                        else:
                            # Other Slack API errors
                            logger.exception("Slack API error")
                            raise SlackClientError(f"Slack API error: {str(e)}")
                    except Exception as e:
                        logger.exception("Unexpected error calling Slack API")
                        raise SlackClientError(f"Unexpected error: {str(e)}")

                # If we've exhausted all retries
                logger.exception(f"Failed after {max_retries} retry attempts")
                raise SlackClientError(
                    f"Rate limit retry attempts exhausted: {str(last_exception)}"
                )

            return cast("F", wrapper)

        return decorator

    @staticmethod
    def find_threaded_messages(messages: list[dict[str, Any]]) -> list[dict[str, Any]]:
        return [
            msg
            for msg in messages
            if msg.get("thread_ts") and msg.get("thread_ts") == msg.get("ts")
        ]

    @_handle_slack_api_errors_with_retry(max_retries=5)
    def get_channel_info(self, channel_id: str) -> dict[str, Any]:
        """
        Get information about a Slack channel.
        Returns:
            Channel information
        Raises:
            SlackClientError: If the channel cannot be found or another error occurs
        """
        response = self._client.conversations_info(channel=channel_id)
        return response["channel"]

    @_handle_slack_api_errors_with_retry(max_retries=5)
    def join_channel(self, channel_id: str) -> None:
        """
        Join a Slack channel.
        Raises:
            SlackClientError: If the channel cannot be joined
        """
        try:
            self._client.conversations_join(channel=channel_id)
            logger.info(f"Successfully joined channel {channel_id}")
        except SlackApiError as e:
            error = e.response.get("error")
            if error == "already_in_channel":
                logger.debug(f"Already in channel {channel_id}")
            elif error == "method_not_supported_for_channel_type":
                logger.warning(
                    f"Cannot join channel {channel_id} - it's likely a private channel."
                )
            elif error == "missing_scope":
                logger.exception(
                    f"Missing 'channels:join' scope to join channel {channel_id}. "
                    f"Check your bot token permissions."
                )
                raise SlackClientError(f"Missing scope to join channel: {str(e)}")
            elif error == "channel_not_found":
                logger.exception(
                    f"Channel {channel_id} not found or bot has no visibility."
                )
                raise SlackClientError(f"Channel not found: {str(e)}")
            else:
                logger.exception(f"Unexpected error when joining channel {channel_id}")
                raise SlackClientError(f"Failed to join channel: {str(e)}")
        except Exception as e:
            logger.exception(f"Unexpected error joining channel {channel_id}")
            raise SlackClientError(f"Unexpected error: {str(e)}")

    @_handle_slack_api_errors_with_retry(max_retries=5)
    def get_channel_history(
        self,
        channel_id: str,
        limit: int = 100,
        oldest: Optional[str] = None,
        latest: Optional[str] = None,
    ) -> list[dict[str, Any]]:
        """
        Get message history for a channel.
        Returns:
            List of message objects
        Raises:
            SlackClientError: If the message history cannot be fetched
        """
        messages: list[dict[str, Any]] = []
        cursor = None

        while True:
            response = self._client.conversations_history(
                channel=channel_id,
                limit=min(limit - len(messages), 100),
                oldest=oldest,
                latest=latest,
                cursor=cursor,
            )

            # Cast to help the type checker understand this is a dictionary
            response_dict = cast("dict[str, Any]", response)
            new_messages = response_dict.get("messages", [])
            messages.extend(new_messages)

            # Check if we need to paginate
            response_metadata = response_dict.get("response_metadata", {})
            if isinstance(response_metadata, dict):
                cursor = response_metadata.get("next_cursor")
            else:
                cursor = None

            if not cursor or len(messages) >= limit:
                break

        return messages[:limit]

    @_handle_slack_api_errors_with_retry(max_retries=5)
    def get_thread_replies(
        self, channel_id: str, thread_ts: str, limit: int = 100
    ) -> list[dict[str, Any]]:
        """
        Get replies to a message in a thread.
        Returns:
            List of reply message objects (excluding the parent message)
        Raises:
            SlackClientError: If the replies cannot be fetched
        """
        replies: list[dict[str, Any]] = []
        cursor = None

        while True:
            response = self._client.conversations_replies(
                channel=channel_id,
                ts=thread_ts,
                limit=min(
                    limit - len(replies) + 1, 100
                ),  # +1 to account for parent message
                cursor=cursor,
            )

            # Cast to help the type checker understand this is a dictionary
            response_dict = cast("dict[str, Any]", response)
            thread_messages = response_dict.get("messages", [])

            # Skip the parent message (first one) in the first request
            if not replies and thread_messages:
                thread_messages = thread_messages[1:]

            replies.extend(thread_messages)

            # Check if we need to paginate
            response_metadata = response_dict.get("response_metadata", {})
            if isinstance(response_metadata, dict):
                cursor = response_metadata.get("next_cursor")
            else:
                cursor = None

            if not cursor or len(replies) >= limit:
                break

        return replies[:limit]
