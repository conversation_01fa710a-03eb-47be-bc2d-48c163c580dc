import datetime

from pgvector.sqlalchemy import Vector
from sqlalchemy import ARRAY, DateTime, Index, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class Document(TenantModel):
    __tablename__ = "document"

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource), nullable=False, index=True
    )
    document_id: Mapped[str] = mapped_column(String, index=True, nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    tags: Mapped[list[str]] = mapped_column(ARRAY(String), nullable=True)
    source_timestamp: Mapped[datetime.datetime] = mapped_column(
        DateTime(timezone=True), nullable=False
    )
    embedding: Mapped[list[float]] = mapped_column(Vector(768), nullable=True)

    __table_args__ = (
        Index("idx_tenant_document_id", "tenant_id", "document_id"),
        Index("idx_tenant_source", "tenant_id", "source"),
        Index("idx_tags", "tags", postgresql_using="gin"),
        Index(
            "idx_embedding",
            "embedding",
            postgresql_using="ivfflat",
            postgresql_with={"lists": 100},
        ),
    )
