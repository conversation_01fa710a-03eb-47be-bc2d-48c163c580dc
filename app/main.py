from fastapi import FastAP<PERSON>

from app.admin import register_admin
from app.agentic.lifespan import lifespan
from app.agentic.router import router as agentic_router
from app.auth.router import router as auth_router
from app.common.exceptions import register_exception_handlers
from app.core.config import config
from app.core.cors import register_cors
from app.core.database import engine
from app.core.logging_config import setup_logging_config
from app.workspace.router import (
    router as workspace_router,
)

setup_logging_config(config)


def get_application() -> FastAPI:
    application = FastAPI(
        title=config.project_title, debug=config.debug, lifespan=lifespan
    )

    application.include_router(
        workspace_router, prefix="/workspace", tags=["workspace"]
    )
    application.include_router(auth_router, prefix="/auth", tags=["auth"])
    application.include_router(agentic_router, prefix="/agent", tags=["agent"])

    register_cors(application)
    register_exception_handlers(application)
    register_admin(application, engine)

    return application


app = get_application()
