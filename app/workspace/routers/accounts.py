from fastapi import APIRouter

from app.workspace.dependencies import AccountServiceDep
from app.workspace.schemas import AccountRead

router = APIRouter()


@router.get(
    "/accounts",
    response_model=list[AccountRead],
    name="get_accounts",
)
async def get_accounts(service: AccountServiceDep):
    return await service.get_accounts()


@router.get(
    "/sync_accounts",
    response_model=None,
    name="sync_accounts",
)
async def sync_accounts(service: AccountServiceDep):
    return await service.sync_accounts()
