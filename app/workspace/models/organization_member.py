import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.auth.models import User
from app.core.database import BaseModel
from app.workspace.models.organization import Organization


class OrganizationMember(BaseModel):
    __tablename__ = "organization_member"

    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("user.id"), nullable=False, index=True
    )
    organization_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization.id"),
        nullable=False,
        index=True,
    )

    user: Mapped[User] = relationship(User)
    organization: Mapped[Organization] = relationship("Organization")

    is_admin: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    def __repr__(self):
        return f"<Organization(id='{self.id}', name='{self.user}')>"

    __table_args__ = (
        UniqueConstraint("user_id", "organization_id", name="uix_user_organization"),
    )
