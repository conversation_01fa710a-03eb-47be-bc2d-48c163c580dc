from enum import Enum

from app.integrations.types import IntegrationSource


class EnvironmentType(str, Enum):
    SANDBOX = "sandbox"
    PROD = "prod"


class IntegrationType(str, Enum):
    CRM = "crm"
    MESSAGING = "messaging"


CRM_SOURCES = {
    IntegrationSource.SALESFORCE,
    IntegrationSource.HUBSPOT,
}

MESSAGING_SOURCES = {
    IntegrationSource.SLACK,
    IntegrationSource.TEAMS,
}

SOURCE_TYPE_MAP = {
    IntegrationSource.SALESFORCE: IntegrationType.CRM,
    IntegrationSource.HUBSPOT: IntegrationType.CRM,
    IntegrationSource.SLACK: IntegrationType.MESSAGING,
    IntegrationSource.TEAMS: IntegrationType.MESSAGING,
}
