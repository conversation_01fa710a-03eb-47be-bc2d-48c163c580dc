# ruff: noqa: S106

import uuid
from datetime import UTC, datetime, timedelta

import pytest

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.salesforce_connection import (
    SalesforceConnectionService,
    SalesforceTokenResponse,
)
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )


@pytest.fixture
def service_mocks(mocker):
    """Fixture pour tous les mocks nécessaires au service."""
    db_session_mock = mocker.Mock()
    db_session_mock.commit = mocker.AsyncMock()
    db_session_mock.refresh = mocker.AsyncMock()

    return {
        "db_session": db_session_mock,
        "integration_user_repo": mocker.Mock(),
        "integration_cfg_repo": mocker.Mock(),
        "oauth_flow_manager": mocker.Mock(),
    }


@pytest.fixture
def salesforce_connection_service(service_mocks):
    service = SalesforceConnectionService(
        db_session=service_mocks["db_session"],
        integration_user_repo=service_mocks["integration_user_repo"],
        integration_cfg_repo=service_mocks["integration_cfg_repo"],
        auth_url="https://login.salesforce.com/services/oauth2/authorize",
        token_url="https://login.salesforce.com/services/oauth2/token",
        redirect_uri="https://app.example.com/oauth/callback",
        flow_type=OAuthFlowType.STANDARD,
    )
    service.oauth_flow_manager = service_mocks["oauth_flow_manager"]

    return service


@pytest.fixture
def test_data(mock_environment):
    user_id = uuid.uuid4()
    integration_config_id = uuid.uuid4()
    integration_user_id = uuid.uuid4()

    integration_config = IntegrationConfig()
    integration_config.id = integration_config_id
    integration_config.organization_id = mock_environment.organization_id
    integration_config.source = IntegrationSource.SALESFORCE
    integration_config.credentials = {
        "client_id": "fake_client_id",
        "client_secret": "fake_client_secret",
    }

    integration_user = IntegrationUser()
    integration_user.id = integration_user_id
    integration_user.user_id = user_id
    integration_user.integration_config_id = integration_config_id
    integration_user.external_user_id = "SF_USER_123"
    integration_user.external_org_id = "SF_ORG_456"
    integration_user.access_token = "fake_access_token"
    integration_user.refresh_token = "fake_refresh_token"
    integration_user.instance_url = "https://example.my.salesforce.com"
    integration_user.scope = "refresh_token full"
    integration_user.token_type = "Bearer"
    integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)
    integration_user.last_refreshed_at = datetime.now(UTC)

    return {
        "user_id": user_id,
        "environment": mock_environment,
        "integration_config_id": integration_config_id,
        "integration_user_id": integration_user_id,
        "integration_config": integration_config,
        "integration_user": integration_user,
    }


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = service_mocks[
        "integration_cfg_repo"
    ].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "oauth_flow_manager"
    ].generate_authorization_uri.return_value = "https://example.com/auth"

    result = await salesforce_connection_service.generate_oauth_authorization_uri(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
    )

    assert result == "https://example.com/auth"
    service_mocks["integration_cfg_repo"].get_by_org_and_source.assert_called_once_with(
        test_data["environment"].organization_id,
        IntegrationSource.SALESFORCE,
        test_data["environment"].type,
    )
    service_mocks["oauth_flow_manager"].generate_authorization_uri.assert_called_once()


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_generate_oauth_authorization_uri_no_config(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationConfigError):
        await salesforce_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_process_oauth_callback_new_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    created_integration_user = IntegrationUser()
    created_integration_user.external_user_id = "SF_USER_123"
    created_integration_user.external_org_id = "SF_ORG_456"
    created_integration_user.access_token = "new_access_token"
    created_integration_user.refresh_token = "new_refresh_token"
    created_integration_user.instance_url = "https://example.my.salesforce.com"
    created_integration_user.scope = "refresh_token full"
    created_integration_user.token_type = "Bearer"
    created_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].create = mocker.AsyncMock(
        return_value=created_integration_user
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "new_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].create.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_update_existing_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "access_token": "updated_access_token",
        "refresh_token": "updated_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    updated_integration_user = IntegrationUser()
    updated_integration_user.external_user_id = "SF_USER_123"
    updated_integration_user.external_org_id = "SF_ORG_456"
    updated_integration_user.access_token = "updated_access_token"
    updated_integration_user.refresh_token = "updated_refresh_token"
    updated_integration_user.instance_url = "https://example.my.salesforce.com"
    updated_integration_user.scope = "refresh_token full"
    updated_integration_user.token_type = "Bearer"
    updated_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=updated_integration_user
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "updated_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_process_oauth_callback_no_config(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationConfigError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_refresh_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )

    token_data = {
        "access_token": "refreshed_access_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].refresh_access_token = mocker.AsyncMock(
        return_value=token_data
    )

    refreshed_integration_user = IntegrationUser()
    refreshed_integration_user.external_user_id = "SF_USER_123"
    refreshed_integration_user.external_org_id = "SF_ORG_456"
    refreshed_integration_user.access_token = "refreshed_access_token"
    refreshed_integration_user.refresh_token = "fake_refresh_token"
    refreshed_integration_user.instance_url = "https://example.my.salesforce.com"
    refreshed_integration_user.scope = "refresh_token full"
    refreshed_integration_user.token_type = "Bearer"
    refreshed_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=refreshed_integration_user
    )

    result = await salesforce_connection_service.refresh_access_token(
        integration_user_id=test_data["integration_user_id"],
        environment=test_data["environment"],
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "refreshed_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].get_by_id.assert_called_once_with(
        test_data["integration_user_id"]
    )
    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(
        refreshed_integration_user
    )


@pytest.mark.anyio
async def test_refresh_access_token_not_found(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationTokenNotFoundError):
        await salesforce_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_refresh_access_token_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )
