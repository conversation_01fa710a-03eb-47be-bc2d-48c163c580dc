import pytest

from app.integrations.types import IntegrationSource
from app.workspace.validators.integration import validate_credentials, validate_settings


def test_empty_credentials():
    result = validate_credentials(IntegrationSource.SALESFORCE, {})
    assert result == {}


def test_none_credentials():
    result = validate_credentials(IntegrationSource.SALESFORCE, None)
    assert result == {}


def test_salesforce_valid_credentials_oauth():
    credentials = {"client_id": "test_id", "client_secret": "test_secret"}
    result = validate_credentials(IntegrationSource.SALESFORCE, credentials)
    assert result == credentials
    assert "username" not in result


def test_salesforce_valid_credentials_username_password():
    credentials = {
        "username": "<EMAIL>",
        "password": "password123",
        "security_token": "security123",
    }
    result = validate_credentials(IntegrationSource.SALESFORCE, credentials)
    assert result == credentials
    assert "client_id" not in result


def test_salesforce_invalid_credentials_extra_fields():
    credentials = {
        "client_id": "test_id",
        "client_secret": "test_secret",
        "extra_field": "should_not_be_here",
    }
    with pytest.raises(ValueError) as exc_info:
        validate_credentials(IntegrationSource.SALESFORCE, credentials)
    assert "extra_field" in str(exc_info.value).lower()


def test_slack_valid_credentials():
    credentials = {"slack_token": "xoxb-test-token"}
    result = validate_credentials(IntegrationSource.SLACK, credentials)
    assert result == credentials


def test_slack_invalid_credentials_extra_field():
    credentials = {"extra_field": "should_not_be_here"}
    with pytest.raises(ValueError) as exc_info:
        validate_credentials(IntegrationSource.SLACK, credentials)
    assert "slack_token" in str(exc_info.value).lower()


def test_unknown_source_credentials():
    source = "UNKNOWN_SOURCE"
    credentials = {"key": "value"}
    result = validate_credentials(source, credentials)
    assert result == credentials


def test_empty_settings():
    result = validate_settings(IntegrationSource.SALESFORCE, {})
    assert result == {}


def test_none_settings():
    result = validate_settings(IntegrationSource.SALESFORCE, None)
    assert result == {}


def test_salesforce_valid_settings():
    settings = {}
    result = validate_settings(IntegrationSource.SALESFORCE, settings)
    assert result == settings


def test_salesforce_invalid_settings_extra_fields():
    settings = {"unexpected_field": "value"}
    with pytest.raises(ValueError) as exc_info:
        validate_settings(IntegrationSource.SALESFORCE, settings)
    assert "unexpected_field" in str(exc_info.value).lower()


def test_slack_valid_settings():
    settings = {}
    result = validate_settings(IntegrationSource.SLACK, settings)
    assert result == settings


def test_unknown_source_settings():
    source = "UNKNOWN_SOURCE"
    settings = {"key": "value"}
    result = validate_settings(source, settings)
    assert result == settings
