import pytest

from app.main import app
from app.workspace.dependencies import AccountServiceDep


@pytest.mark.anyio
async def test_get_accounts_success(
    async_client, test_app, override_account_service_success
):
    url = test_app.url_path_for("get_accounts")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    data = response.json()
    assert isinstance(data, list)
    assert len(data) == 2
    names = [account["crm_name"] for account in data]
    assert "Account1" in names
    assert "Account2" in names
    override_account_service_success.get_accounts.assert_called_once()


@pytest.mark.anyio
async def test_sync_accounts_success(
    async_client, test_app, override_account_service_success
):
    url = test_app.url_path_for("sync_accounts")
    response = await async_client.get(url)
    assert response.status_code == 200, response.text
    override_account_service_success.sync_accounts.assert_called_once()


@pytest.mark.anyio
async def test_sync_accounts_endpoint_error(async_client, test_app, mocker):
    url = test_app.url_path_for("get_accounts")
    service_mock = mocker.Mock()
    service_mock.sync_accounts.side_effect = Exception
    app.dependency_overrides[AccountServiceDep] = lambda: service_mock
    response = await async_client.get(url)
    assert response.status_code == 500
    app.dependency_overrides.pop(AccountServiceDep)
