from uuid import UUID

from app.workspace.integrations.user_integrations import (
    UserIntegrations,
)
from app.workspace.schemas import AccountRead


class AccountService:
    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations

    async def get_accounts(self) -> list[AccountRead]:
        provider = await self.user_integrations.crm()
        crm_user_id = await self.user_integrations.crm_user_id()
        if not provider or not crm_user_id:
            return []

        accounts = await provider.list_account_access(crm_user_id=crm_user_id)
        return [
            AccountRead(crm_id=account["Id"], crm_name=account["Name"])
            for account in accounts
        ]

    async def sync_accounts(self) -> None:
        await self.user_integrations.sync_crm_accounts()
