import importlib
import pkgutil

from fastapi import FastAPI
from sqladmin import Admin, ModelView
from sqlalchemy import Engine


def register_admin(
    app: FastAPI, engine: Engine, package_names: list[str] | None = None
):
    admin_views = _autodiscover_admin_views(package_names)
    admin_views.sort(key=lambda view: getattr(view, "autodiscover_order", 100))
    admin = Admin(app, engine)
    for view in admin_views:
        admin.add_view(view)


class BaseModelView(ModelView):
    form_excluded_columns = ["updated_at", "created_at"]


def _autodiscover_admin_views(
    package_names: list[str] | None = None,
) -> list[type[ModelView]]:
    # If no package_names provided, discover all 'app.*.admin' modules
    if package_names is None:
        package_names = []
        # Import the top-level 'app' package
        app_pkg = importlib.import_module("app")
        # Iterate through all submodules and subpackages in 'app'
        for _, modname, _ in pkgutil.walk_packages(
            app_pkg.__path__, app_pkg.__name__ + "."
        ):
            if modname.startswith("app.admin"):
                continue
            if modname.endswith(".admin"):
                package_names.append(modname)

    modules_to_inspect = []
    for package_name in package_names:
        module = importlib.import_module(package_name)
        if hasattr(module, "__path__"):  # It's a package
            for _, name, _ in pkgutil.walk_packages(
                module.__path__, module.__name__ + "."
            ):
                modules_to_inspect.append(importlib.import_module(name))
        else:  # It's a module
            modules_to_inspect.append(module)

    admin_views = []
    for module in modules_to_inspect:
        for attr_name in dir(module):
            attribute = getattr(module, attr_name)
            if (
                isinstance(attribute, type)
                and issubclass(attribute, ModelView)
                and attribute is not ModelView
                and hasattr(attribute, "model")
            ):
                admin_views.append(attribute)

    return admin_views
