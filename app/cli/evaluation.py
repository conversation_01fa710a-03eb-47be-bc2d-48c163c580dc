import asyncio
import uuid

import typer

from app.agentic.evals.pipeline import LangfuseEvaluationPipeline
from app.workspace.types import EnvironmentType

app = typer.Typer(help="Evaluation management commands", no_args_is_help=True)


@app.command()
def run_once(
    dataset_name: str = typer.Argument(
        ..., help="Name of the Langfuse dataset to evaluate"
    ),
    user_id: uuid.UUID = typer.Option(..., help="User ID"),
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    crm_account_id: str = typer.Option(..., help="CRM account ID to evaluate"),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
):
    try:
        typer.echo(f"[blue]Starting evaluation for dataset: {dataset_name}[/blue]")

        result = asyncio.run(
            _run_evaluation_once(dataset_name, org_id, user_id, crm_account_id, env)
        )

        if result["errors"]:
            typer.echo(
                f"[yellow]⚠ {len(result['errors'])} errors occurred during evaluation[/yellow]"
            )

    except Exception as e:
        typer.echo(f"[red]✗ Evaluation failed: {e}[/red]")
        raise typer.Exit(code=1)


async def _run_evaluation_once(
    dataset_name: str,
    org_id: uuid.UUID,
    user_id: uuid.UUID,
    crm_account_id: str,
    env: EnvironmentType,
):
    async with LangfuseEvaluationPipeline(
        dataset_name=dataset_name,
        org_id=org_id,
        user_id=user_id,
        crm_account_id=crm_account_id,
        env_type=env,
    ) as langfuse_pipeline:
        result = await langfuse_pipeline.run_evaluation()
        return result
