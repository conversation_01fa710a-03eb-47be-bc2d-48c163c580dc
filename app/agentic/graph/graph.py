from collections.abc import Awaitable, Callable
from functools import partial
from typing import Any, cast
from uuid import UUID

from langchain_core.runnables import Runnable
from langchain_core.tools import BaseTool, StructuredTool
from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import START, StateGraph
from langgraph.prebuilt import create_react_agent
from langgraph.pregel import Pregel
from langgraph_supervisor import create_supervisor
from pydantic import SecretStr

from app.agentic.context.tools import ToolRegistry
from app.agentic.graph.nodes import context_injector_node, fetch_account_node
from app.agentic.graph.prompts import AgentPrompts
from app.agentic.graph.state import ConversationState
from app.core.config import config
from app.workspace.integrations.user_integrations import UserIntegrations


class GraphFactory:
    def __init__(
        self,
        user_id: UUID,
        user_integrations: UserIntegrations,
    ):
        self.user_id = user_id
        self.user_integrations = user_integrations

    async def create_graph(self) -> StateGraph:
        sales_doc_agent = self._create_sales_document_agent()
        supervisor_agent = await self._create_supervisor_agent([sales_doc_agent])

        graph = StateGraph(ConversationState)

        fetch_account: Callable[[ConversationState], Awaitable[ConversationState]] = (
            partial(fetch_account_node, user_integrations=self.user_integrations)
        )

        graph.add_node("fetch_account", fetch_account)
        graph.add_node("context_injector", context_injector_node)
        graph.add_node("supervisor", supervisor_agent)

        graph.add_conditional_edges(
            START,
            self._needs_account_refresh,
            {True: "fetch_account", False: "supervisor"},
        )
        graph.add_edge("fetch_account", "context_injector")
        graph.add_edge("context_injector", "supervisor")

        return graph

    @staticmethod
    def _get_llm() -> ChatGoogleGenerativeAI:
        return ChatGoogleGenerativeAI(
            model="gemini-2.5-flash-preview-04-17",
            api_key=SecretStr(config.gemini_api_key),
        )

    async def _create_supervisor_agent(self, agents: list[Pregel]) -> Runnable:
        tools = await self.get_langchain_tools()
        return create_supervisor(
            agents=agents,
            model=self._get_llm(),
            tools=tools,
            prompt=AgentPrompts.get_supervisor_system_prompt(),
            state_schema=ConversationState,
            supervisor_name="supervisor",
        ).compile()

    def _create_sales_document_agent(self) -> Pregel:
        return create_react_agent(
            model=self._get_llm(),
            tools=[],
            prompt=AgentPrompts.get_sales_document_agent_prompt(),
            name="sales_document_agent_runnable",
            state_schema=ConversationState,
        )

    async def get_langchain_tools(self) -> list[BaseTool | Callable[..., Any]]:
        registry = ToolRegistry(self.user_id, self.user_integrations)
        raw_tools = await registry.get_tools()
        langchain_tools: list[BaseTool] = [
            StructuredTool(
                name=td.name,
                description=td.description,
                func=None,
                coroutine=td.coroutine,
                args_schema=td.args_schema,
            )
            for td in raw_tools
        ]
        return cast("list[BaseTool | Callable[..., Any]]", langchain_tools)

    @staticmethod
    def _needs_account_refresh(state: ConversationState) -> bool:
        return not state.get("last_refetch_at") or not state.get("account_info")
