import datetime
import uuid
from unittest.mock import AsyncMock

import pytest
from pytest_mock import Mo<PERSON><PERSON><PERSON><PERSON>

from app.auth.dependencies import get_authenticated_user_id
from app.integrations.protocols import CRMResource
from app.main import app
from app.workspace.dependencies import get_organization_service
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.schemas import OrganizationRead
from app.workspace.services.organization import OrganizationService


@pytest.fixture
def org_id():
    return uuid.uuid4()


@pytest.fixture
def user_id():
    return uuid.uuid4()


@pytest.fixture
def org_member_id():
    return uuid.uuid4()


@pytest.fixture
def mock_crm_provider(mocker):
    mock_provider = mocker.Mock(spec=CRMResource)

    mock_provider.get_opportunity = AsyncMock(
        return_value={"Id": "001", "Name": "Test Opportunity"}
    )
    mock_provider.update_opportunity = AsyncMock(
        return_value={"Id": "001", "Name": "Updated Opportunity"}
    )
    mock_provider.list_opportunities_by_account = AsyncMock(
        return_value=[{"Id": "001", "Name": "Test Opportunity"}]
    )
    mock_provider.search_opportunities = AsyncMock(return_value=[])
    mock_provider.get_account = AsyncMock(
        return_value={"Id": "002", "Name": "Test Account"}
    )
    mock_provider.update_account = AsyncMock(
        return_value={"Id": "002", "Name": "Updated Account"}
    )
    mock_provider.search_accounts = AsyncMock(return_value=[])
    mock_provider.get_contact = AsyncMock(
        return_value={"Id": "003", "Name": "Test Contact"}
    )
    mock_provider.create_contact = AsyncMock(
        return_value={"Id": "003", "Name": "New Contact"}
    )
    mock_provider.update_contact = AsyncMock(
        return_value={"Id": "003", "Name": "Updated Contact"}
    )
    mock_provider.list_contacts_by_account = AsyncMock(return_value=[])
    mock_provider.search_contacts = AsyncMock(return_value=[])
    mock_provider.get_task = AsyncMock(
        return_value={"Id": "00T", "Subject": "Test Task"}
    )
    mock_provider.create_task = AsyncMock(
        return_value={"Id": "00T", "Subject": "New Task"}
    )
    mock_provider.update_task = AsyncMock(
        return_value={"Id": "00T", "Subject": "Updated Task"}
    )
    mock_provider.list_tasks_by_contact = AsyncMock(return_value=[])
    mock_provider.list_tasks_by_account = AsyncMock(return_value=[])
    mock_provider.get_event = AsyncMock(
        return_value={"Id": "00U", "Subject": "Test Event"}
    )
    mock_provider.create_event = AsyncMock(
        return_value={"Id": "00U", "Subject": "New Event"}
    )
    mock_provider.update_event = AsyncMock(
        return_value={"Id": "00U", "Subject": "Updated Event"}
    )
    mock_provider.list_events_by_contact = AsyncMock(return_value=[])
    mock_provider.list_events_by_account = AsyncMock(return_value=[])

    return mock_provider


@pytest.fixture
def mock_user_integrations_instance(mocker, mock_crm_provider):
    instance = mocker.Mock(spec=UserIntegrations)

    async def mock_crm():
        return mock_crm_provider

    instance.crm = mock_crm
    instance.environment = mocker.MagicMock()
    instance.environment.id = uuid.uuid4()

    instance.get_crm_accounts = AsyncMock(return_value=[])

    return instance


@pytest.fixture(autouse=True)
def mock_database_session(mocker):
    """Mock AsyncSessionLocal used in crm_tools.py"""
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session
    return mock_session_local


@pytest.fixture(autouse=True)
def mock_create_user_integrations(mocker, mock_user_integrations_instance):
    """Mock create_user_integrations function used in crm_tools.py"""
    mock = mocker.patch("app.agentic.context.crm_tools.create_user_integrations")
    mock.return_value = mock_user_integrations_instance
    return mock


@pytest.fixture(autouse=True)
def override_authenticated_user_id():
    def mock_get_authenticated_user_id():
        return "mocked_user_id"

    app.dependency_overrides[get_authenticated_user_id] = mock_get_authenticated_user_id
    yield
    app.dependency_overrides.pop(get_authenticated_user_id)


@pytest.fixture(autouse=True)
def override_organization_service(mocker: MockerFixture):
    mock_service = mocker.MagicMock(spec=OrganizationService)
    now = datetime.datetime.now(datetime.UTC)
    org = OrganizationRead(
        id=uuid.uuid4(),
        name="Test Org",
        domain="test.org",
        is_active=True,
        created_at=now,
        updated_at=now,
    )

    mock_service.get_user_organization.return_value = org

    app.dependency_overrides[get_organization_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_organization_service)
