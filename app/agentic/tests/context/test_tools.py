import asyncio

import pytest
from pydantic import BaseModel, Field

from app.agentic.context.schemas import (
    CreateContact,
    CreateEvent,
    CreateTask,
    GetAccount,
    GetContact,
    GetCurrentUserTerritory,
    GetEvent,
    GetOpportunity,
    GetTask,
    ListContactsByAccount,
    ListEventsByAccount,
    ListEventsByContact,
    ListOpportunitiesByAccount,
    ListTasksByAccount,
    ListTasksByContact,
    SearchAccounts,
    SearchContacts,
    SearchOpportunities,
    ToolDefinition,
    UpdateAccount,
    UpdateContact,
    UpdateEvent,
    UpdateOpportunity,
    UpdateTask,
)
from app.agentic.context.tools import LinkupSearchInput, ToolRegistry


@pytest.mark.anyio
async def test_get_tools(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = LinkupSearchInput

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm = mocker.AsyncMock(return_value=mocker.Mock())
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()

    # Should have 22 CRM tools + 1 web search tool + 1 user validation tool + 1 territory tool = 25 tools
    assert len(tools) == 25

    expected_crm_tools = {
        "get_opportunity": ("Fetch a CRM opportunity by its ID", GetOpportunity),
        "update_opportunity": (
            "Update a CRM opportunity with provided fields",
            UpdateOpportunity,
        ),
        "list_opportunities_by_account": (
            "List CRM opportunities for a given account",
            ListOpportunitiesByAccount,
        ),
        "search_opportunities": (
            "Search CRM opportunities by criteria",
            SearchOpportunities,
        ),
        "get_account": ("Fetch a CRM account by its ID", GetAccount),
        "update_account": ("Update a CRM account with provided fields", UpdateAccount),
        "search_accounts": ("Search CRM accounts by criteria", SearchAccounts),
        "get_contact": ("Fetch a CRM contact by its ID", GetContact),
        "create_contact": ("Create a new CRM contact", CreateContact),
        "update_contact": ("Update a CRM contact with provided fields", UpdateContact),
        "list_contacts_by_account": (
            "List CRM contacts for a given account",
            ListContactsByAccount,
        ),
        "search_contacts": ("Search CRM contacts by criteria", SearchContacts),
        "get_task": ("Fetch a CRM task by its ID", GetTask),
        "create_task": ("Create a new CRM task", CreateTask),
        "update_task": ("Update a CRM task with provided fields", UpdateTask),
        "list_tasks_by_contact": (
            "List CRM tasks for a given contact",
            ListTasksByContact,
        ),
        "list_tasks_by_account": (
            "List CRM tasks for a given account",
            ListTasksByAccount,
        ),
        "get_event": ("Fetch a CRM event by its ID", GetEvent),
        "create_event": ("Create a new CRM event", CreateEvent),
        "update_event": ("Update a CRM event with provided fields", UpdateEvent),
        "list_events_by_contact": (
            "List CRM events for a given contact",
            ListEventsByContact,
        ),
        "list_events_by_account": (
            "List CRM events for a given account",
            ListEventsByAccount,
        ),
    }

    tool_names = [tool.name for tool in tools]

    for expected_name in expected_crm_tools:
        assert expected_name in tool_names

    assert "search_web" in tool_names
    assert "get_current_user_territory" in tool_names
    assert "request_user_validation" in tool_names

    crm_tools = [tool for tool in tools if tool.name in expected_crm_tools]
    for tool in crm_tools:
        assert isinstance(tool, ToolDefinition)
        expected_description, expected_schema = expected_crm_tools[tool.name]
        assert tool.description == expected_description
        assert tool.args_schema == expected_schema
        assert tool.coroutine is not None
        assert asyncio.iscoroutinefunction(tool.coroutine)

    search_web_tool = next(tool for tool in tools if tool.name == "search_web")
    assert isinstance(search_web_tool, ToolDefinition)
    assert search_web_tool.name == "search_web"
    assert search_web_tool.description == "Search the web for information"
    assert search_web_tool.args_schema == LinkupSearchInput
    assert search_web_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(search_web_tool.coroutine)

    get_current_user_territory_tool = next(
        tool for tool in tools if tool.name == "get_current_user_territory"
    )
    assert isinstance(get_current_user_territory_tool, ToolDefinition)
    assert get_current_user_territory_tool.name == "get_current_user_territory"
    assert (
        get_current_user_territory_tool.description
        == "Get my sales territory - shows which accounts I have access to"
    )
    assert get_current_user_territory_tool.args_schema == GetCurrentUserTerritory
    assert get_current_user_territory_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(get_current_user_territory_tool.coroutine)

    validation_tool = next(
        tool for tool in tools if tool.name == "request_user_validation"
    )
    assert isinstance(validation_tool, ToolDefinition)
    assert validation_tool.name == "request_user_validation"
    assert (
        "Ask the end-user to approve or reject a proposed CRM update"
        in validation_tool.description
    )
    assert validation_tool.coroutine is not None
    assert asyncio.iscoroutinefunction(validation_tool.coroutine)


@pytest.mark.anyio
async def test_tool_functions(
    mocker,
    user_id,
    mock_user_integrations_instance,
    mock_crm_provider,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()

    result_1 = mocker.MagicMock()
    result_1.name = "Test Result 1"
    result_1.url = "https://example.com/1"
    result_1.content = "This is test content 1"

    result_2 = mocker.MagicMock()
    result_2.name = "Test Result 2"
    result_2.url = "https://example.com/2"
    result_2.content = "This is test content 2"

    mock_result.results = [result_1, result_2]
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()

    async def mock_crm():
        return mock_crm_provider

    mock_user_integrations.crm = mock_crm
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_crm_provider.get_opportunity = mocker.AsyncMock(return_value={"id": "001"})
    mock_crm_provider.update_opportunity = mocker.AsyncMock(return_value={"id": "001"})
    mock_crm_provider.list_opportunities_by_account = mocker.AsyncMock(return_value=[])
    mock_crm_provider.search_opportunities = mocker.AsyncMock(return_value=[])
    mock_crm_provider.get_account = mocker.AsyncMock(return_value={"id": "002"})
    mock_crm_provider.update_account = mocker.AsyncMock(return_value={"id": "002"})
    mock_crm_provider.search_accounts = mocker.AsyncMock(return_value=[])
    mock_crm_provider.get_contact = mocker.AsyncMock(return_value={"id": "003"})
    mock_crm_provider.create_contact = mocker.AsyncMock(return_value={"id": "003"})
    mock_crm_provider.update_contact = mocker.AsyncMock(return_value={"id": "003"})
    mock_crm_provider.list_contacts_by_account = mocker.AsyncMock(return_value=[])
    mock_crm_provider.search_contacts = mocker.AsyncMock(return_value=[])
    mock_crm_provider.get_task = mocker.AsyncMock(return_value={"id": "00T"})
    mock_crm_provider.create_task = mocker.AsyncMock(return_value={"id": "00T"})
    mock_crm_provider.update_task = mocker.AsyncMock(return_value={"id": "00T"})
    mock_crm_provider.list_tasks_by_contact = mocker.AsyncMock(return_value=[])
    mock_crm_provider.list_tasks_by_account = mocker.AsyncMock(return_value=[])
    mock_crm_provider.get_event = mocker.AsyncMock(return_value={"id": "00U"})
    mock_crm_provider.create_event = mocker.AsyncMock(return_value={"id": "00U"})
    mock_crm_provider.update_event = mocker.AsyncMock(return_value={"id": "00U"})
    mock_crm_provider.list_events_by_contact = mocker.AsyncMock(return_value=[])
    mock_crm_provider.list_events_by_account = mocker.AsyncMock(return_value=[])

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()
    tool_map = {t.name: t for t in tools}

    await tool_map["get_opportunity"].coroutine("001")
    mock_crm_provider.get_opportunity.assert_called_once_with("001")

    fields = {"Name": "Updated Opportunity"}
    await tool_map["update_opportunity"].coroutine("001", fields)
    mock_crm_provider.update_opportunity.assert_called_once_with("001", fields)

    await tool_map["list_opportunities_by_account"].coroutine("account123")
    mock_crm_provider.list_opportunities_by_account.assert_called_once_with(
        "account123"
    )

    search_opp_criteria = {"Name": "Big Deal"}
    await tool_map["search_opportunities"].coroutine(search_opp_criteria)
    mock_crm_provider.search_opportunities.assert_called_once_with(search_opp_criteria)

    await tool_map["get_account"].coroutine("002")
    mock_crm_provider.get_account.assert_called_once_with("002")

    account_fields = {"Name": "Updated Account"}
    await tool_map["update_account"].coroutine("002", account_fields)
    mock_crm_provider.update_account.assert_called_once_with("002", account_fields)

    search_acc_criteria = {"Name": "Tech Corp"}
    await tool_map["search_accounts"].coroutine(search_acc_criteria)
    mock_crm_provider.search_accounts.assert_called_once_with(search_acc_criteria)

    await tool_map["get_contact"].coroutine("003")
    mock_crm_provider.get_contact.assert_called_once_with("003")

    contact_data = {"FirstName": "John", "LastName": "Doe"}
    await tool_map["create_contact"].coroutine(contact_data)
    mock_crm_provider.create_contact.assert_called_once_with(contact_data)

    contact_update_data = {"FirstName": "Jane"}
    await tool_map["update_contact"].coroutine("003", contact_update_data)
    mock_crm_provider.update_contact.assert_called_once_with("003", contact_update_data)

    await tool_map["list_contacts_by_account"].coroutine("002")
    mock_crm_provider.list_contacts_by_account.assert_called_once_with("002")

    search_criteria = {"Email": "<EMAIL>"}
    await tool_map["search_contacts"].coroutine(search_criteria)
    mock_crm_provider.search_contacts.assert_called_once_with(search_criteria)

    await tool_map["get_task"].coroutine("00T")
    mock_crm_provider.get_task.assert_called_once_with("00T")

    task_data = {"Subject": "Call client", "Status": "Not Started"}
    await tool_map["create_task"].coroutine(task_data)
    mock_crm_provider.create_task.assert_called_once_with(task_data)

    task_update_data = {"Status": "Completed"}
    await tool_map["update_task"].coroutine("00T", task_update_data)
    mock_crm_provider.update_task.assert_called_once_with("00T", task_update_data)

    await tool_map["list_tasks_by_contact"].coroutine("003")
    mock_crm_provider.list_tasks_by_contact.assert_called_once_with("003")

    await tool_map["list_tasks_by_account"].coroutine("001")
    mock_crm_provider.list_tasks_by_account.assert_called_once_with("001")

    await tool_map["get_event"].coroutine("00U")
    mock_crm_provider.get_event.assert_called_once_with("00U")

    event_data = {"Subject": "Client Meeting", "StartDateTime": "2024-01-15T10:00:00Z"}
    await tool_map["create_event"].coroutine(event_data)
    mock_crm_provider.create_event.assert_called_once_with(event_data)

    event_update_data = {"Subject": "Updated Meeting"}
    await tool_map["update_event"].coroutine("00U", event_update_data)
    mock_crm_provider.update_event.assert_called_once_with("00U", event_update_data)

    await tool_map["list_events_by_contact"].coroutine("003")
    mock_crm_provider.list_events_by_contact.assert_called_once_with("003")

    await tool_map["list_events_by_account"].coroutine("001")
    mock_crm_provider.list_events_by_account.assert_called_once_with("001")

    result = await tool_map["search_web"].coroutine("test query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "test query"})

    expected_result = (
        "Title: Test Result 1\nURL: https://example.com/1\nContent: This is test content 1\n\n\n"
        "Title: Test Result 2\nURL: https://example.com/2\nContent: This is test content 2\n"
    )
    assert result == expected_result


@pytest.mark.anyio
async def test_get_current_user_territory(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_accounts = [
        {"id": "acc_1", "name": "Territory Account 1", "territory": "North"},
        {"id": "acc_2", "name": "Territory Account 2", "territory": "South"},
    ]

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(
        return_value=mock_accounts
    )

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = mocker.MagicMock()

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()

    tool_map = {t.name: t for t in tools}
    get_current_user_territory_tool = tool_map["get_current_user_territory"]

    result = await get_current_user_territory_tool.coroutine()

    mock_create_user_integrations.assert_called_once()
    mock_user_integrations.get_crm_accounts.assert_called_once()

    assert result == mock_accounts


@pytest.mark.anyio
async def test_get_current_user_territory_empty_list(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mock_create_user_integrations = mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = mocker.MagicMock()

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()

    tool_map = {t.name: t for t in tools}
    get_current_user_territory_tool = tool_map["get_current_user_territory"]

    result = await get_current_user_territory_tool.coroutine()

    mock_create_user_integrations.assert_called_once()
    mock_user_integrations.get_crm_accounts.assert_called_once()

    assert result == []


@pytest.mark.anyio
async def test_search_web_no_results(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    mock_result.results = []
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm = mocker.AsyncMock(return_value=mocker.Mock())
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results query")
    mock_linkup_tool.invoke.assert_called_once_with({"query": "no results query"})
    assert result == "No search results found."


@pytest.mark.anyio
async def test_search_web_no_results_attribute(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"

    class SearchWebSchema(BaseModel):
        query: str = Field(description="The search query")

    mock_linkup_tool.args_schema = SearchWebSchema

    mock_result = mocker.MagicMock()
    del mock_result.results
    mock_linkup_tool.invoke.return_value = mock_result

    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm = mocker.AsyncMock(return_value=mocker.Mock())
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])

    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()
    tool_map = {t.name: t for t in tools}
    search_web_tool = tool_map["search_web"]

    result = await search_web_tool.coroutine("no results attribute query")
    mock_linkup_tool.invoke.assert_called_once_with(
        {"query": "no results attribute query"}
    )
    assert result == "No search results found."


@pytest.mark.anyio
async def test_user_validation_tool(
    mocker,
    user_id,
    mock_user_integrations_instance,
):
    mock_interrupt = mocker.patch("app.agentic.context.tools.interrupt")
    mock_interrupt.return_value = "approve"

    mock_linkup_tool = mocker.MagicMock()
    mock_linkup_tool.description = "Search the web for information"
    mock_linkup_tool.args_schema = mocker.MagicMock()
    mocker.patch(
        "app.agentic.context.tools.LinkupSearchTool", return_value=mock_linkup_tool
    )

    mock_session = mocker.MagicMock()
    mock_session_local = mocker.patch("app.agentic.context.crm_tools.AsyncSessionLocal")
    mock_session_local.return_value.__aenter__.return_value = mock_session

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm = mocker.AsyncMock(return_value=mocker.Mock())
    mock_user_integrations.get_crm_accounts = mocker.AsyncMock(return_value=[])
    mocker.patch(
        "app.agentic.context.crm_tools.create_user_integrations",
        return_value=mock_user_integrations,
    )

    registry = ToolRegistry(user_id, mock_user_integrations_instance)
    tools = await registry.get_tools()
    tool_map = {t.name: t for t in tools}

    validation_tool = tool_map["request_user_validation"]

    update_details = {"field": "value", "action": "update"}
    result = await validation_tool.coroutine(update_details)

    mock_interrupt.assert_called_once_with(
        {
            "question": "Do these updates in your CRM look correct?",
            "update_to_be_made": update_details,
        }
    )
    assert result == "APPROVED: User validated the CRM update."

    mock_interrupt.reset_mock()
    mock_interrupt.return_value = "reject"

    result = await validation_tool.coroutine(update_details)
    assert (
        result
        == "REJECTED: User rejected the update. Revise your approach or ask for more info."
    )

    mock_interrupt.reset_mock()
    mock_interrupt.side_effect = Exception("Test error")

    result = await validation_tool.coroutine(update_details)
    assert result == "ERROR: Could not obtain validation: Test error"
