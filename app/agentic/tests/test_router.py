import json
import uuid
from datetime import UTC, datetime

import pytest

from app.agentic.dependencies import get_agent_service
from app.agentic.schemas import Cha<PERSON><PERSON><PERSON><PERSON>, ThreadRead, ThreadsRead
from app.agentic.service import AgentService
from app.main import app
from app.workspace.dependencies import get_user_org_id


@pytest.fixture
def override_agent_service(mocker):
    mock_service = mocker.Mock(spec=AgentService)
    app.dependency_overrides[get_agent_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_agent_service)


@pytest.fixture
def override_user_org_id(org_id):
    app.dependency_overrides[get_user_org_id] = lambda: org_id
    yield org_id
    app.dependency_overrides.pop(get_user_org_id)


@pytest.mark.anyio
async def test_chat_stream_endpoint_success(async_client, override_agent_service):
    async def mock_sse_iterator():
        metadata_data = {"thread_id": "test-thread-123"}
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"

        data = {"content": "Test "}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

        data = {"content": "stream "}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

        data = {"content": "response"}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

    override_agent_service.process_message_stream.return_value = mock_sse_iterator()

    request_data = ChatRequest(
        message="Test stream message",
        thread_id=str(uuid.uuid4()),
        crm_account_id="test-account-123",
    )
    response = await async_client.post(
        app.url_path_for("chat_stream"), json=request_data.model_dump(mode="json")
    )

    assert "x-vercel-ai-data-stream" in response.headers
    assert response.headers["x-vercel-ai-data-stream"] == "v1"

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    assert response.content

    override_agent_service.process_message_stream.assert_called_once_with(request_data)


@pytest.mark.anyio
async def test_get_threads_endpoint_success(async_client, override_agent_service):
    test_query_account_id = "123456789012345678"

    thread1 = ThreadRead(
        id="thread-1",
        organization_member_id=uuid.uuid4(),
        crm_account_id="test-account-123",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    thread2 = ThreadRead(
        id="thread-2",
        organization_member_id=uuid.uuid4(),
        crm_account_id="test-account-456",
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    threads_response_data = ThreadsRead(threads=[thread1, thread2])

    override_agent_service.get_threads_by_org_member_and_crm_account.return_value = (
        threads_response_data
    )

    response = await async_client.get(
        app.url_path_for("threads"), params={"account_id": test_query_account_id}
    )

    assert response.status_code == 200
    response_json = response.json()
    assert "threads" in response_json
    threads_list = response_json["threads"]
    assert len(threads_list) == 2
    assert threads_list[0]["id"] == "thread-1"
    assert threads_list[0]["crm_account_id"] == "test-account-123"
    assert threads_list[1]["id"] == "thread-2"
    assert threads_list[1]["crm_account_id"] == "test-account-456"

    override_agent_service.get_threads_by_org_member_and_crm_account.assert_called_once_with(
        test_query_account_id
    )


@pytest.mark.anyio
async def test_get_thread_history_endpoint_success(
    async_client, override_agent_service
):
    thread_id = "test-thread-123"

    mock_response = {
        "pagination": {
            "thread_id": thread_id,
            "current_page": 1,
            "page_size": 20,
            "total_messages": 2,
            "total_pages": 1,
        },
        "messages": [
            {"id": "0", "role": "user", "content": "Hello"},
            {"id": "1", "role": "assistant", "content": "Hi there!"},
        ],
    }

    override_agent_service.get_thread_history.return_value = mock_response

    response = await async_client.get(
        app.url_path_for("thread_history", thread_id=thread_id)
    )

    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"

    response_data = response.json()
    assert response_data == mock_response

    override_agent_service.get_thread_history.assert_called_once_with(thread_id, 1, 20)
