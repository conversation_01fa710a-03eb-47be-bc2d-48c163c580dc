from typing import Any, Literal

from langchain_linkup.search_tool import LinkupSearchInput, LinkupSearchTool
from langgraph.types import interrupt

from app.agentic.context.base import IToolBuilder
from app.agentic.context.crm_tools import CR<PERSON>oolBuilder
from app.agentic.context.schemas import (
    RequestUserValidation,
    ToolDefinition,
)
from app.core.config import config


class WebSearchToolBuilder(IToolBuilder):
    def __init__(
        self,
        api_key: str = config.linkup_api_key,
        depth: Literal["standard", "deep"] = "standard",
        output_type: Literal[
            "searchResults", "sourcedAnswer", "structured"
        ] = "searchResults",
    ):
        self.api_key = api_key
        self.depth = depth
        self.output_type = output_type

    async def build_tools(self) -> list[ToolDefinition]:
        linkup_tool = LinkupSearchTool(
            depth=self.depth,
            output_type=self.output_type,
            linkup_api_key=self.api_key,
        )

        async def search_web(query: str) -> str:
            result = linkup_tool.invoke({"query": query})

            if hasattr(result, "results") and result.results:
                search_results = []
                for item in result.results:
                    search_results.append(
                        f"Title: {item.name}\nURL: {item.url}\nContent: {item.content}\n"
                    )
                return "\n\n".join(search_results)
            return "No search results found."

        return [
            ToolDefinition(
                name="search_web",
                coroutine=search_web,
                description=linkup_tool.description,
                args_schema=LinkupSearchInput,
            )
        ]


class UserValidationToolBuilder(IToolBuilder):
    async def build_tools(self) -> list[ToolDefinition]:
        async def request_user_validation(
            update_details: dict[str, Any],
        ) -> str:
            try:
                decision = interrupt(
                    {
                        "question": "Do these updates in your CRM look correct?",
                        "update_to_be_made": update_details,
                    }
                )
                if decision == "approve":
                    return "APPROVED: User validated the CRM update."
                return (
                    "REJECTED: User rejected the update. "
                    "Revise your approach or ask for more info."
                )
            except Exception as exc:
                return f"ERROR: Could not obtain validation: {exc}"

        return [
            ToolDefinition(
                name="request_user_validation",
                description=(
                    "Ask the end-user to approve or reject a proposed "
                    "CRM update before executing it"
                ),
                coroutine=request_user_validation,
                args_schema=RequestUserValidation,
            )
        ]


class ToolRegistry:
    def __init__(self, user_id, user_integrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.builders: list[IToolBuilder] = []

    def _register_builder(self, builder: IToolBuilder) -> None:
        self.builders.append(builder)

    async def _build_all_tools(self) -> list[ToolDefinition]:
        all_tools = []
        for builder in self.builders:
            tools = await builder.build_tools()
            all_tools.extend(tools)
        return all_tools

    async def get_tools(self):
        # self._register_builder(WebSearchToolBuilder())
        self._register_builder(CRMToolBuilder(self.user_id, self.user_integrations))
        self._register_builder(UserValidationToolBuilder())

        return await self._build_all_tools()
