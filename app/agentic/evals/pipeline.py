import uuid
from typing import Any

from langchain_core.messages import HumanMessage
from langfuse import Langfuse

from app.agentic.graph.graph import GraphFactory
from app.agentic.graph.graph_manager import GraphManager
from app.auth.repository import UserRepository
from app.common.helpers.logger import get_logger
from app.common.oauth.flow_manager import OAuthFlowType
from app.core.config import config
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    create_user_integrations,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.repositories.organization_member import OrganizationMemberRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.organization import OrganizationService
from app.workspace.services.organization_team import OrganizationTeamService
from app.workspace.services.salesforce_connection import SalesforceConnectionService
from app.workspace.types import EnvironmentType

logger = get_logger()


class LangfuseEvaluationPipeline:
    def __init__(
        self,
        dataset_name: str,
        user_id: uuid.UUID,
        org_id: uuid.UUID,
        crm_account_id: str,
        env_type: EnvironmentType,
    ):
        self.dataset_name = dataset_name
        self.org_id = org_id
        self.user_id = user_id
        self.crm_account_id = crm_account_id
        self.env_type = env_type
        self.langfuse_client = Langfuse(
            public_key=config.langfuse_public_key,
            secret_key=config.langfuse_secret_key,
            host=config.langfuse_host,
        )

        self.db_session = AsyncSessionLocal()

        integration_cfg_repo = IntegrationConfigRepository(self.db_session)
        integration_user_repo = IntegrationUserRepository(self.db_session)
        org_repo = OrganizationRepository(self.db_session)
        org_member_repo = OrganizationMemberRepository(self.db_session)
        env_repo = EnvironmentRepository(self.db_session)
        user_repo = UserRepository(self.db_session)

        self.org_service = OrganizationService(
            db_session=self.db_session,
            org_repo=org_repo,
            env_repo=env_repo,
        )
        self.integration_cfg_service = IntegrationConfigService(
            integration_cfg_repo=integration_cfg_repo,
            integration_user_repo=integration_user_repo,
        )
        self.org_team_service = OrganizationTeamService(
            db_session=self.db_session,
            org_member_repo=org_member_repo,
            org_repo=org_repo,
            user_repo=user_repo,
        )
        self.salesforce_connection_service = SalesforceConnectionService(
            db_session=self.db_session,
            integration_user_repo=integration_user_repo,
            integration_cfg_repo=integration_cfg_repo,
            auth_url=str(config.salesforce.auth_url),
            token_url=str(config.salesforce.token_url),
            redirect_uri=str(config.salesforce.redirect_uri),
            flow_type=OAuthFlowType.PKCE,
        )

        self.environment: OrgEnvironment | None = None

    async def _initialize(self) -> None:
        if self.environment is None:
            self.environment = await self._get_environment()

            if self.environment is None:
                raise RuntimeError("Failed to initialize environment")

    async def _get_environment(self) -> OrgEnvironment:
        environment = await self.org_service.get_env(
            org_id=self.org_id, env_type=self.env_type
        )
        if not environment:
            raise RuntimeError(
                f"No environment found for org {self.org_id} and type {self.env_type}"
            )
        return environment

    async def run_evaluation(self) -> dict[str, Any]:
        if self.environment is None:
            await self._initialize()

        if self.environment is None:
            raise RuntimeError("Failed to initialize environment")

        try:
            logger.info(f"Starting Langfuse evaluation for dataset {self.dataset_name}")

            results = {
                "dataset_name": self.dataset_name,
                "total_items": 0,
                "evaluated_items": 0,
                "average_score": 0.0,
                "scores": [],
                "errors": [],
            }

            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment.id,
                db_session=self.db_session,
            )

            logger.info(f"User integrations: {user_integrations}")

            graph_factory = GraphFactory(self.user_id, user_integrations)
            graph_definition = await graph_factory.create_graph()
            compiled_graph = graph_definition.compile()

            graph_manager = GraphManager(graph=compiled_graph, langfuse_handler=None)

            dataset = self.langfuse_client.get_dataset(self.dataset_name)

            total_score = 0.0
            evaluated_count = 0

            for item in dataset.items:
                logger.info(f"Evaluating item: {item.input}")

                input_messages = [HumanMessage(item.input)]

                graph_input = {
                    "messages": input_messages,
                    "crm_account_id": self.crm_account_id,
                    "thread_id": None,
                    "resume": False,
                    "org_id": self.org_id,
                    "user_id": self.user_id,
                }

                output = await graph_manager.invoke_graph(graph_input)
                logger.info("output >>>", output)

            return results

        except Exception:
            logger.exception("Error during Langfuse evaluation")
            raise

    async def _close(self):
        if self.db_session:
            await self.db_session.close()

    async def __aenter__(self):
        await self._initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self._close()
