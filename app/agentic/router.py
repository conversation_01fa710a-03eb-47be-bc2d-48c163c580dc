from fastapi import APIRouter, Query
from starlette.responses import StreamingResponse

from app.agentic.dependencies import AgentServiceDep
from app.agentic.schemas import (
    ChatRequest,
    ThreadHistoryResponse,
    ThreadsRead,
)

router = APIRouter()


@router.post("/chat_stream", name="chat_stream")
async def chat_stream(
    request: ChatRequest,
    agent_service: AgentServiceDep,
):
    chat_stream_iterator = await agent_service.process_message_stream(request)

    response = StreamingResponse(chat_stream_iterator, media_type="text/event-stream")
    response.headers["x-vercel-ai-data-stream"] = "v1"
    return response


@router.get("/threads", name="threads", response_model=ThreadsRead | None)
async def get_threads(
    agent_service: AgentServiceDep,
    account_id: str = Query(min_length=18, max_length=18, description="CRM account ID"),
):
    return await agent_service.get_threads_by_org_member_and_crm_account(account_id)


@router.get(
    "/threads/{thread_id}/messages",
    name="thread_history",
    response_model=ThreadHistoryResponse | None,
)
async def get_thread_history(
    thread_id: str,
    agent_service: AgentServiceDep,
    page: int = Query(1, ge=1, description="Page number for message history"),
    size: int = Query(20, ge=1, le=100, description="Number of messages per page"),
):
    return await agent_service.get_thread_history(thread_id, page, size)
